import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/update_dialog.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/flexible_update_dialog.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/forced_update_dialog.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/update_banner.dart';

/// Example screen demonstrating the update dialog system
/// This screen allows testing different update scenarios
class UpdateDialogsExampleScreen extends ConsumerWidget {
  const UpdateDialogsExampleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Update Dialogs Example'),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Current state card
            _buildStateCard(updateState),
            const SizedBox(height: 24),
            
            // Update banner demo
            const Text(
              'Update Banner Demo',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const UpdateBanner(),
            const SizedBox(height: 24),
            
            // Dialog demos
            const Text(
              'Dialog Demos',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Auto-select dialog
            ElevatedButton.icon(
              onPressed: () => showUpdateDialog(context),
              icon: const Icon(Icons.auto_awesome),
              label: const Text('Show Auto-Selected Dialog'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1976D2),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 12),
            
            // Flexible dialog
            ElevatedButton.icon(
              onPressed: () => showFlexibleUpdateDialog(context),
              icon: const Icon(Icons.system_update),
              label: const Text('Show Flexible Update Dialog'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 12),
            
            // Forced dialog
            ElevatedButton.icon(
              onPressed: () => showForcedUpdateDialog(context),
              icon: const Icon(Icons.warning),
              label: const Text('Show Forced Update Dialog'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 24),
            
            // Controller actions
            const Text(
              'Controller Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Initialize
            ElevatedButton.icon(
              onPressed: updateState.isLoading 
                  ? null 
                  : () => updateController.initialize(),
              icon: const Icon(Icons.refresh),
              label: const Text('Initialize Update Service'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 12),
            
            // Check for updates
            ElevatedButton.icon(
              onPressed: updateState.isLoading 
                  ? null 
                  : () => updateController.checkForUpdates(),
              icon: const Icon(Icons.search),
              label: const Text('Check for Updates'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 12),
            
            // Start flexible update
            if (updateState.isUpdateAvailable && !updateState.isUpdateDownloaded)
              ElevatedButton.icon(
                onPressed: updateState.isLoading 
                    ? null 
                    : () => updateController.startFlexibleUpdate(),
                icon: const Icon(Icons.download),
                label: const Text('Start Flexible Update'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            
            // Complete flexible update
            if (updateState.isUpdateDownloaded)
              ElevatedButton.icon(
                onPressed: updateState.isLoading 
                    ? null 
                    : () => updateController.completeFlexibleUpdate(),
                icon: const Icon(Icons.install_desktop),
                label: const Text('Complete Flexible Update'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            
            const SizedBox(height: 12),
            
            // Clear error
            if (updateState.error != null)
              ElevatedButton.icon(
                onPressed: () => updateController.clearError(),
                icon: const Icon(Icons.clear),
                label: const Text('Clear Error'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStateCard(InAppUpdateState updateState) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Current Update State',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildStateRow('Loading', updateState.isLoading),
            _buildStateRow('Update Available', updateState.isUpdateAvailable),
            _buildStateRow('Update Downloaded', updateState.isUpdateDownloaded),
            _buildStateRow('High Priority', updateState.isHighPriority),
            if (updateState.error != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Error: ${updateState.error}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStateRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: value ? Colors.black87 : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
