import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/flexible_update_dialog.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/forced_update_dialog.dart';

/// Main update dialog that automatically selects the appropriate dialog type
/// based on update priority and availability
class UpdateDialog extends ConsumerWidget {
  final bool? isForced;

  const UpdateDialog({
    super.key,
    this.isForced,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    // Determine if this should be a forced update
    final shouldForce = isForced ?? 
        (updateState.isUpdateAvailable && updateController.shouldForceImmediateUpdate);

    if (shouldForce) {
      return const ForcedUpdateDialog();
    } else {
      return const FlexibleUpdateDialog();
    }
  }
}

/// Show update dialog with automatic type selection
/// 
/// This function automatically chooses between flexible and forced update dialogs
/// based on the update priority and current state.
/// 
/// Parameters:
/// - [context]: The build context
/// - [isForced]: Optional override to force a specific dialog type
/// 
/// Returns a Future that completes when the dialog is dismissed
Future<void> showUpdateDialog(
  BuildContext context, {
  bool? isForced,
}) async {
  return showDialog<void>(
    context: context,
    barrierDismissible: isForced != true, // Allow dismissal only for flexible updates
    builder: (BuildContext context) => UpdateDialog(isForced: isForced),
  );
}

/// Show update dialog specifically for flexible updates
Future<void> showFlexibleUpdateDialogWrapper(BuildContext context) async {
  return showFlexibleUpdateDialog(context);
}

/// Show update dialog specifically for forced updates
Future<void> showForcedUpdateDialogWrapper(BuildContext context) async {
  return showForcedUpdateDialog(context);
}
