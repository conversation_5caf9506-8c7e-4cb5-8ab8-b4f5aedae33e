import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';

/// Modern dialog for flexible (optional) app updates
/// Features Google Play branding and user-friendly interface
class FlexibleUpdateDialog extends ConsumerWidget {
  const FlexibleUpdateDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 8,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Google Play Logo
            _buildGooglePlayLogo(),
            const SizedBox(height: 16),

            // Title
            Text(
              updateState.isUpdateDownloaded
                  ? 'Update Ready to Install'
                  : 'Update Available',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Description
            Text(
              updateState.isUpdateDownloaded
                  ? 'The update has been downloaded and is ready to install. Restart the app to apply the update.'
                  : 'A new version of the app is available with improvements and bug fixes.',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Priority indicator
            if (updateState.isHighPriority)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: const Text(
                  'Recommended',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            const SizedBox(height: 20),

            // Error message
            if (updateState.error != null)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        updateState.error!,
                        style: const TextStyle(fontSize: 12, color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),

            // Action buttons
            Row(
              children: [
                // Later button (only show if update is not downloaded)
                if (!updateState.isUpdateDownloaded)
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Later',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                if (!updateState.isUpdateDownloaded) const SizedBox(width: 12),

                // Update/Install button
                Expanded(
                  flex: updateState.isUpdateDownloaded ? 1 : 2,
                  child: ElevatedButton(
                    onPressed:
                        updateState.isLoading
                            ? null
                            : () => _handleUpdateAction(
                              context,
                              ref,
                              updateController,
                              updateState,
                            ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1976D2), // Google Blue
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                    child:
                        updateState.isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : Text(
                              updateState.isUpdateDownloaded
                                  ? 'Install Now'
                                  : 'Update',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGooglePlayLogo() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Icon(
        Icons.system_update,
        size: 28,
        color: Color(0xFF1976D2),
      ),
    );
  }

  Future<void> _handleUpdateAction(
    BuildContext context,
    WidgetRef ref,
    InAppUpdateController updateController,
    InAppUpdateState updateState,
  ) async {
    if (updateState.isUpdateDownloaded) {
      // Install the downloaded update
      await updateController.completeFlexibleUpdate();
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    } else {
      // Start downloading the update
      await updateController.startFlexibleUpdate();
      // Keep dialog open to show download progress
    }
  }
}

/// Show flexible update dialog
Future<void> showFlexibleUpdateDialog(BuildContext context) async {
  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) => const FlexibleUpdateDialog(),
  );
}
