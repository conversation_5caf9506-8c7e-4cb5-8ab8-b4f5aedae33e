import 'package:flutter/material.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/community_card.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/dashboard_header.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/learning_materials_section.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/dashboard_user_lesson.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/update_banner.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const DecoratedBox(
      decoration: BoxDecoration(color: Colors.white),
      child: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Add const constructors to prevent rebuilds
              DashboardHeader(),
              // Show update banner for flexible updates
              UpdateBanner(),
              <PERSON><PERSON><PERSON><PERSON>(height: 40),
              CommunityCard(),
              DashboardUserLesson(),
              LearningMaterialsSection(),
            ],
          ),
        ),
      ),
    );
  }
}
